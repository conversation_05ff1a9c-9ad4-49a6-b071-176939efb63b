[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "cell-cover-generator"
version = "0.1.0"
description = "A tool to generate cover images using an external API."
requires-python = ">=3.13"
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]

dependencies = [
    "requests>=2.28.2",
    "pillow>=10.0.0",
    "pyperclip>=1.8.2",
    "colorlog>=6.9.0",
    "python-dotenv>=1.0.1",
    "numpy>=1.24.4",
    "tqdm>=4.67.1",
    "typer>=0.15.2",
    "openai>=1.76.0",
]

[project.scripts]
crc = "cell_cover.cli:app"

[tool.setuptools]
packages = {find = {}}
