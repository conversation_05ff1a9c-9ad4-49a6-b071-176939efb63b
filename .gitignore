# Python Bytecode and Cache
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
build/
dist/
*.egg-info/
cell_cover_generator.egg-info/
develop-eggs/
*.egg

# Virtual Environments
.venv/
venv/
env/
ENV/

# Secrets
.env
*.env
.env.*

# Logs
logs/
*.log

# Runtime data / Generated files
metadata/
cell_cover/metadata/
.config/
images/
cell_cover/images/
outputs/
cell_cover/outputs/

# OS / Editor specific
.DS_Store
.idea/
.vscode/
.cursor/mcp.json

# Testing / Linting Cache
.pytest_cache/
.mypy_cache/
.ruff_cache/
.coverage
htmlcov/

*.txt
*.yaml
*.yml
*.ini
*.cfg

*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.tiff
*.ico